[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "screenmonitormcp"
version = "1.0.0"
description = "Revolutionary AI Vision MCP Server - Give AI real-time sight and screen interaction capabilities"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
maintainers = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
keywords = [
    "mcp", "ai", "screen-monitoring", "computer-vision", "automation", 
    "claude", "openai", "screen-capture", "ui-automation", "real-time"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: System :: Monitoring",
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "mss>=9.0.0",
    "Pillow>=10.0.0",
    "python-multipart>=0.0.6",
    "pygetwindow>=0.0.9",
    "python-dotenv>=1.0.0",
    "openai>=1.0.0",
    "mcp[cli]>=0.1.0",
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "pytesseract>=0.3.10",
    "easyocr>=1.7.0",
    "pyautogui>=0.9.54",
    "scikit-learn>=1.3.0",
    "pandas>=2.0.0",
    "structlog>=23.0.0",
    "tenacity>=8.2.0",
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/inkbytefo/ScreenMonitorMCP"
Repository = "https://github.com/inkbytefo/ScreenMonitorMCP"
Documentation = "https://github.com/inkbytefo/ScreenMonitorMCP#readme"
"Bug Tracker" = "https://github.com/inkbytefo/ScreenMonitorMCP/issues"
Changelog = "https://github.com/inkbytefo/ScreenMonitorMCP/blob/main/CHANGELOG.md"

[project.scripts]
screenmonitormcp = "screenmonitormcp.main:main"
smcp = "screenmonitormcp.main:main"

[tool.setuptools]
packages = ["screenmonitormcp"]

[tool.setuptools.package-data]
screenmonitormcp = ["*.md", "*.txt", "*.env.example"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["screenmonitormcp"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
